# Base64.decode
```java
package com.google.android.gms.internal.auth;

import android.util.Base64;

/* loaded from: classes.dex */
public final class zzhv implements zzht {
    public static final zzdc zza;
    public static final zzdc zzb;
    public static final zzdc zzc;
    public static final zzdc zzd;
    public static final zzdc zze;
    public static final zzdc zzf;
    public static final zzdc zzg;
    public static final zzdc zzh;
    public static final zzdc zzi;
    public static final zzdc zzj;
    public static final zzdc zzk;
    public static final zzdc zzl;
    public static final zzdc zzm;
    public static final zzdc zzn;

    static {
        zzcz zzczVarZza = new zzcz(zzcr.zza("com.google.android.gms.auth_account")).zzb().zza();
        zza = zzczVarZza.zzc("getTokenRefactor__account_data_service_sample_percentage", 0.0d);
        zzb = zzczVarZza.zze("getTokenRefactor__account_data_service_tokenAPI_usable", true);
        zzc = zzczVarZza.zzd("getTokenRefactor__account_manager_timeout_seconds", 20L);
        zzd = zzczVarZza.zzd("getTokenRefactor__android_id_shift", 0L);
        zze = zzczVarZza.zze("getTokenRefactor__authenticator_logic_improved", false);
        try {
            zzf = zzczVarZza.zzf("getTokenRefactor__blocked_packages", zzhr.zzk(Base64.decode("ChNjb20uYW5kcm9pZC52ZW5kaW5nCiBjb20uZ29vZ2xlLmFuZHJvaWQuYXBwcy5tZWV0aW5ncwohY29tLmdvb2dsZS5hbmRyb2lkLmFwcHMubWVzc2FnaW5n", 3)), zzhu.zza);
            zzg = zzczVarZza.zze("getTokenRefactor__chimera_get_token_evolved", true);
            zzh = zzczVarZza.zzd("getTokenRefactor__clear_token_timeout_seconds", 20L);
            zzi = zzczVarZza.zzd("getTokenRefactor__default_task_timeout_seconds", 20L);
            zzj = zzczVarZza.zze("getTokenRefactor__gaul_accounts_api_evolved", false);
            zzk = zzczVarZza.zze("getTokenRefactor__gaul_token_api_evolved", false);
            zzl = zzczVarZza.zzd("getTokenRefactor__get_token_timeout_seconds", 120L);
            zzm = zzczVarZza.zze("getTokenRefactor__gms_account_authenticator_evolved", true);
            zzn = zzczVarZza.zzc("getTokenRefactor__gms_account_authenticator_sample_percentage", 0.0d);
        } catch (Exception e10) {
            throw new AssertionError(e10);
        }
    }

    @Override // com.google.android.gms.internal.auth.zzht
    public final zzhr zza() {
        return (zzhr) zzf.zzb();
    }

    @Override // com.google.android.gms.internal.auth.zzht
    public final boolean zzb() {
        return ((Boolean) zzj.zzb()).booleanValue();
    }

    @Override // com.google.android.gms.internal.auth.zzht
    public final boolean zzc() {
        return ((Boolean) zzk.zzb()).booleanValue();
    }
}
```
```java
private static final c7.a f18005a = new e7.d().j(t6.a.f17259a).k(true).i();

    /* JADX INFO: Access modifiers changed from: private */
    interface a {
        Object a(JsonReader jsonReader);
    }

    private static f0.e.d.f A(JsonReader jsonReader) throws IOException {
        f0.e.d.f.a aVarA = f0.e.d.f.a();
        jsonReader.beginObject();
        while (jsonReader.hasNext()) {
            String strNextName = jsonReader.nextName();
            strNextName.hashCode();
            if (strNextName.equals("assignments")) {
                aVarA.b(n(jsonReader, new a() { // from class: u6.c
                    @Override // u6.j.a
                    public final Object a(JsonReader jsonReader2) {
                        return j.z(jsonReader2);
                    }
                }));
            } else {
                jsonReader.skipValue();
            }
        }
        jsonReader.endObject();
        return aVarA.a();
    }

    private static f0.e.d.a.b.AbstractC0293d B(JsonReader jsonReader) throws IOException {
        f0.e.d.a.b.AbstractC0293d.AbstractC0294a abstractC0294aA = f0.e.d.a.b.AbstractC0293d.a();
        jsonReader.beginObject();
        while (jsonReader.hasNext()) {
            String strNextName = jsonReader.nextName();
            strNextName.hashCode();
            switch (strNextName) {
                case "address":
                    abstractC0294aA.b(jsonReader.nextLong());
                    break;
                case "code":
                    abstractC0294aA.c(jsonReader.nextString());
                    break;
                case "name":
                    abstractC0294aA.d(jsonReader.nextString());
                    break;
                default:
                    jsonReader.skipValue();
                    break;
            }
        }
        jsonReader.endObject();
        return abstractC0294aA.a();
    }

    /* JADX INFO: Access modifiers changed from: private */
    public static f0.e.d.a.b.AbstractC0295e C(JsonReader jsonReader) throws IOException {
        f0.e.d.a.b.AbstractC0295e.AbstractC0296a abstractC0296aA = f0.e.d.a.b.AbstractC0295e.a();
        jsonReader.beginObject();
        while (jsonReader.hasNext()) {
            String strNextName = jsonReader.nextName();
            strNextName.hashCode();
            switch (strNextName) {
                case "frames":
                    abstractC0296aA.b(n(jsonReader, new i()));
                    break;
                case "name":
                    abstractC0296aA.d(jsonReader.nextString());
                    break;
                case "importance":
                    abstractC0296aA.c(jsonReader.nextInt());
                    break;
                default:
                    jsonReader.skipValue();
                    break;
            }
        }
        jsonReader.endObject();
        return abstractC0296aA.a();
    }

    /* JADX INFO: Access modifiers changed from: private */
    public static f0.d.b D(JsonReader jsonReader) throws IOException {
        f0.d.b.a aVarA = f0.d.b.a();
        jsonReader.beginObject();
        while (jsonReader.hasNext()) {
            String strNextName = jsonReader.nextName();
            strNextName.hashCode();
            if (strNextName.equals("filename")) {
                aVarA.c(jsonReader.nextString());
            } else if (strNextName.equals("contents")) {
                aVarA.b(Base64.decode(jsonReader.nextString(), 2));
            } else {
                jsonReader.skipValue();
            }
        }
        jsonReader.endObject();
        return aVarA.a();
    }

    private static f0.d E(JsonReader jsonReader) throws IOException {
        f0.d.a aVarA = f0.d.a();
        jsonReader.beginObject();
        while (jsonReader.hasNext()) {
            String strNextName = jsonReader.nextName();
            strNextName.hashCode();
            if (strNextName.equals("files")) {
                aVarA.b(n(jsonReader, new a() { // from class: u6.b
                    @Override // u6.j.a
                    public final Object a(JsonReader jsonReader2) {
                        return j.D(jsonReader2);
                    }
                }));
            } else if (strNextName.equals("orgId")) {
                aVarA.c(jsonReader.nextString());
            } else {
                jsonReader.skipValue();
            }
        }
        jsonReader.endObject();
        return aVarA.a();
    }
```
```java
private static f0.e J(JsonReader jsonReader) throws IOException {
        f0.e.b bVarA = f0.e.a();
        jsonReader.beginObject();
        while (jsonReader.hasNext()) {
            String strNextName = jsonReader.nextName();
            strNextName.hashCode();
            switch (strNextName) {
                case "startedAt":
                    bVarA.m(jsonReader.nextLong());
                    break;
                case "appQualitySessionId":
                    bVarA.c(jsonReader.nextString());
                    break;
                case "identifier":
                    bVarA.k(Base64.decode(jsonReader.nextString(), 2));
                    break;
                case "endedAt":
                    bVarA.f(Long.valueOf(jsonReader.nextLong()));
                    break;
                case "device":
                    bVarA.e(q(jsonReader));
                    break;
                case "events":
                    bVarA.g(n(jsonReader, new a() { // from class: u6.a
                        @Override // u6.j.a
                        public final Object a(JsonReader jsonReader2) {
                            return j.r(jsonReader2);
                        }
                    }));
                    break;
                case "os":
                    bVarA.l(F(jsonReader));
                    break;
                case "app":
                    bVarA.b(l(jsonReader));
                    break;
                case "user":
                    bVarA.n(K(jsonReader));
                    break;
                case "generator":
                    bVarA.h(jsonReader.nextString());
                    break;
                case "crashed":
                    bVarA.d(jsonReader.nextBoolean());
                    break;
                case "generatorType":
                    bVarA.i(jsonReader.nextInt());
                    break;
                default:
                    jsonReader.skipValue();
                    break;
            }
        }
        jsonReader.endObject();
        return bVarA.a();
    }
```
```java
 public static f0.e.d.a.b.AbstractC0289a t(JsonReader jsonReader) throws IOException {
        f0.e.d.a.b.AbstractC0289a.AbstractC0290a abstractC0290aA = f0.e.d.a.b.AbstractC0289a.a();
        jsonReader.beginObject();
        while (jsonReader.hasNext()) {
            String strNextName = jsonReader.nextName();
            strNextName.hashCode();
            switch (strNextName) {
                case "name":
                    abstractC0290aA.c(jsonReader.nextString());
                    break;
                case "size":
                    abstractC0290aA.d(jsonReader.nextLong());
                    break;
                case "uuid":
                    abstractC0290aA.f(Base64.decode(jsonReader.nextString(), 2));
                    break;
                case "baseAddress":
                    abstractC0290aA.b(jsonReader.nextLong());
                    break;
                default:
                    jsonReader.skipValue();
                    break;
            }
        }
        jsonReader.endObject();
        return abstractC0290aA.a();
    }
```
```java
package s4;

import android.util.Base64;

/* loaded from: classes.dex */
public abstract class c {
    public static byte[] a(String str) {
        if (str == null) {
            return null;
        }
        return Base64.decode(str, 10);
    }

    public static byte[] b(String str) {
        if (str == null) {
            return null;
        }
        return Base64.decode(str, 11);
    }

    public static String c(byte[] bArr) {
        if (bArr == null) {
            return null;
        }
        return Base64.encodeToString(bArr, 0);
    }

    public static String d(byte[] bArr) {
        if (bArr == null) {
            return null;
        }
        return Base64.encodeToString(bArr, 10);
    }

    public static String e(byte[] bArr) {
        if (bArr == null) {
            return null;
        }
        return Base64.encodeToString(bArr, 11);
    }
}
```
```java
package com.google.android.play.core.integrity;

import android.app.Activity;
import android.content.Context;
import android.os.Bundle;
import android.os.IBinder;
import android.os.Parcelable;
import android.util.Base64;
import com.google.android.gms.tasks.Task;
import com.google.android.gms.tasks.TaskCompletionSource;
import com.google.android.gms.tasks.Tasks;
import com.google.android.play.integrity.internal.c1;
import com.google.android.play.integrity.internal.g0;
import com.google.android.play.integrity.internal.p0;
import com.google.android.play.integrity.internal.v0;
import java.util.ArrayList;

/* loaded from: classes2.dex */
final class aj {

    /* renamed from: a, reason: collision with root package name */
    final com.google.android.play.integrity.internal.f f6730a;

    /* renamed from: b, reason: collision with root package name */
    private final v0 f6731b;

    /* renamed from: c, reason: collision with root package name */
    private final String f6732c;

    /* renamed from: d, reason: collision with root package name */
    private final Context f6733d;

    /* renamed from: e, reason: collision with root package name */
    private final at f6734e;

    /* renamed from: f, reason: collision with root package name */
    private final k f6735f;

    aj(Context context, v0 v0Var, at atVar, k kVar) {
        this.f6732c = context.getPackageName();
        this.f6731b = v0Var;
        this.f6734e = atVar;
        this.f6735f = kVar;
        this.f6733d = context;
        if (com.google.android.play.integrity.internal.j.b(context)) {
            this.f6730a = new com.google.android.play.integrity.internal.f(context, v0Var, "IntegrityService", ak.f6736a, new c1() { // from class: com.google.android.play.core.integrity.ae
                @Override // com.google.android.play.integrity.internal.c1
                public final Object a(IBinder iBinder) {
                    return p0.H0(iBinder);
                }
            }, null);
        } else {
            v0Var.b("Phonesky is not installed.", new Object[0]);
            this.f6730a = null;
        }
    }

    static /* bridge */ /* synthetic */ Bundle a(aj ajVar, byte[] bArr, Long l10, Parcelable parcelable) {
        Bundle bundle = new Bundle();
        bundle.putString("package.name", ajVar.f6732c);
        bundle.putByteArray("nonce", bArr);
        bundle.putInt("playcore.integrity.version.major", 1);
        bundle.putInt("playcore.integrity.version.minor", 4);
        bundle.putInt("playcore.integrity.version.patch", 0);
        if (l10 != null) {
            bundle.putLong("cloud.prj", l10.longValue());
        }
        if (parcelable != null) {
            bundle.putParcelable("network", parcelable);
        }
        ArrayList arrayList = new ArrayList();
        g0.b(3, arrayList);
        bundle.putParcelableArrayList("event_timestamps", new ArrayList<>(g0.a(arrayList)));
        return bundle;
    }

    final Task b(Activity activity, Bundle bundle) {
        if (this.f6730a == null) {
            return Tasks.forException(new IntegrityServiceException(-2, null));
        }
        int i10 = bundle.getInt("dialog.intent.type");
        this.f6731b.d("requestAndShowDialog(%s, %s)", this.f6732c, Integer.valueOf(i10));
        TaskCompletionSource taskCompletionSource = new TaskCompletionSource();
        this.f6730a.t(new ag(this, taskCompletionSource, bundle, activity, taskCompletionSource, i10), taskCompletionSource);
        return taskCompletionSource.getTask();
    }

    public final Task c(IntegrityTokenRequest integrityTokenRequest) {
        if (this.f6730a == null) {
            return Tasks.forException(new IntegrityServiceException(-2, null));
        }
        if (com.google.android.play.integrity.internal.j.a(this.f6733d) < 82380000) {
            return Tasks.forException(new IntegrityServiceException(-14, null));
        }
        try {
            byte[] bArrDecode = Base64.decode(integrityTokenRequest.nonce(), 10);
            Long lCloudProjectNumber = integrityTokenRequest.cloudProjectNumber();
            if (integrityTokenRequest instanceof ao) {
            }
            this.f6731b.d("requestIntegrityToken(%s)", integrityTokenRequest);
            TaskCompletionSource taskCompletionSource = new TaskCompletionSource();
            this.f6730a.t(new af(this, taskCompletionSource, bArrDecode, lCloudProjectNumber, null, taskCompletionSource, integrityTokenRequest), taskCompletionSource);
            return taskCompletionSource.getTask();
        } catch (IllegalArgumentException e10) {
            return Tasks.forException(new IntegrityServiceException(-13, e10));
        }
    }
}
```

```java
public Task k(Intent intent) {
        String stringExtra = intent.getStringExtra("gcm.rawData64");
        if (stringExtra != null) {
            intent.putExtra("rawData", Base64.decode(stringExtra, 0));
            intent.removeExtra("gcm.rawData64");
        }
        return l(this.f7380a, intent);
    }
```

```java
package com.revenuecat.purchases.common.verification;

import android.util.Base64;
import java.util.Arrays;
import kotlin.jvm.internal.j;
import kotlin.jvm.internal.r;

/* loaded from: classes2.dex */
public final class Signature {
    public static final Companion Companion = new Companion(null);
    private final byte[] intermediateKey;
    private final byte[] intermediateKeyExpiration;
    private final byte[] intermediateKeySignature;
    private final byte[] payload;
    private final byte[] salt;

    public static final class Companion {
        private Companion() {
        }

        public /* synthetic */ Companion(j jVar) {
            this();
        }

        public final Signature fromString$purchases_defaultsRelease(String signature) throws InvalidSignatureSizeException {
            r.f(signature, "signature");
            byte[] signatureBytes = Base64.decode(signature, 0);
            int totalSize = Component.Companion.getTotalSize();
            if (signatureBytes.length == totalSize) {
                r.e(signatureBytes, "signatureBytes");
                return new Signature(SignatureKt.copyOf(signatureBytes, Component.INTERMEDIATE_KEY), SignatureKt.copyOf(signatureBytes, Component.INTERMEDIATE_KEY_EXPIRATION), SignatureKt.copyOf(signatureBytes, Component.INTERMEDIATE_KEY_SIGNATURE), SignatureKt.copyOf(signatureBytes, Component.SALT), SignatureKt.copyOf(signatureBytes, Component.PAYLOAD));
            }
            throw new InvalidSignatureSizeException("Invalid signature size. Expected " + totalSize + ", got " + signatureBytes.length + " bytes");
        }
    }

    public enum Component {
        INTERMEDIATE_KEY(32),
        INTERMEDIATE_KEY_EXPIRATION(4),
        INTERMEDIATE_KEY_SIGNATURE(64),
        SALT(16),
        PAYLOAD(64);

        public static final Companion Companion = new Companion(null);
        private final int size;

        public static final class Companion {
            private Companion() {
            }

            public /* synthetic */ Companion(j jVar) {
                this();
            }

            public final int getTotalSize() {
                int size = 0;
                for (Component component : Component.values()) {
                    size += component.getSize();
                }
                return size;
            }
        }

        Component(int i10) {
            this.size = i10;
        }

        public final int getEndByte() {
            return getStartByte() + this.size;
        }

        public final int getSize() {
            return this.size;
        }

        public final int getStartByte() {
            int i10 = 0;
            for (Object obj : u9.j.h(values(), 0, ordinal())) {
                i10 += ((Component) obj).size;
            }
            return i10;
        }
    }

    public Signature(byte[] intermediateKey, byte[] intermediateKeyExpiration, byte[] intermediateKeySignature, byte[] salt, byte[] payload) {
        r.f(intermediateKey, "intermediateKey");
        r.f(intermediateKeyExpiration, "intermediateKeyExpiration");
        r.f(intermediateKeySignature, "intermediateKeySignature");
        r.f(salt, "salt");
        r.f(payload, "payload");
        this.intermediateKey = intermediateKey;
        this.intermediateKeyExpiration = intermediateKeyExpiration;
        this.intermediateKeySignature = intermediateKeySignature;
        this.salt = salt;
        this.payload = payload;
    }

    public static /* synthetic */ Signature copy$default(Signature signature, byte[] bArr, byte[] bArr2, byte[] bArr3, byte[] bArr4, byte[] bArr5, int i10, Object obj) {
        if ((i10 & 1) != 0) {
            bArr = signature.intermediateKey;
        }
        if ((i10 & 2) != 0) {
            bArr2 = signature.intermediateKeyExpiration;
        }
        byte[] bArr6 = bArr2;
        if ((i10 & 4) != 0) {
            bArr3 = signature.intermediateKeySignature;
        }
        byte[] bArr7 = bArr3;
        if ((i10 & 8) != 0) {
            bArr4 = signature.salt;
        }
        byte[] bArr8 = bArr4;
        if ((i10 & 16) != 0) {
            bArr5 = signature.payload;
        }
        return signature.copy(bArr, bArr6, bArr7, bArr8, bArr5);
    }

    public final byte[] component1() {
        return this.intermediateKey;
    }

    public final byte[] component2() {
        return this.intermediateKeyExpiration;
    }

    public final byte[] component3() {
        return this.intermediateKeySignature;
    }

    public final byte[] component4() {
        return this.salt;
    }

    public final byte[] component5() {
        return this.payload;
    }

    public final Signature copy(byte[] intermediateKey, byte[] intermediateKeyExpiration, byte[] intermediateKeySignature, byte[] salt, byte[] payload) {
        r.f(intermediateKey, "intermediateKey");
        r.f(intermediateKeyExpiration, "intermediateKeyExpiration");
        r.f(intermediateKeySignature, "intermediateKeySignature");
        r.f(salt, "salt");
        r.f(payload, "payload");
        return new Signature(intermediateKey, intermediateKeyExpiration, intermediateKeySignature, salt, payload);
    }

    public boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        if (!r.b(Signature.class, obj != null ? obj.getClass() : null)) {
            return false;
        }
        r.d(obj, "null cannot be cast to non-null type com.revenuecat.purchases.common.verification.Signature");
        Signature signature = (Signature) obj;
        return Arrays.equals(this.intermediateKey, signature.intermediateKey) && Arrays.equals(this.intermediateKeyExpiration, signature.intermediateKeyExpiration) && Arrays.equals(this.intermediateKeySignature, signature.intermediateKeySignature) && Arrays.equals(this.salt, signature.salt) && Arrays.equals(this.payload, signature.payload);
    }

    public final byte[] getIntermediateKey() {
        return this.intermediateKey;
    }

    public final byte[] getIntermediateKeyExpiration() {
        return this.intermediateKeyExpiration;
    }

    public final byte[] getIntermediateKeySignature() {
        return this.intermediateKeySignature;
    }

    public final byte[] getPayload() {
        return this.payload;
    }

    public final byte[] getSalt() {
        return this.salt;
    }

    public int hashCode() {
        return (((((((Arrays.hashCode(this.intermediateKey) * 31) + Arrays.hashCode(this.intermediateKeyExpiration)) * 31) + Arrays.hashCode(this.intermediateKeySignature)) * 31) + Arrays.hashCode(this.salt)) * 31) + Arrays.hashCode(this.payload);
    }

    public String toString() {
        return "Signature(intermediateKey=" + Arrays.toString(this.intermediateKey) + ", intermediateKeyExpiration=" + Arrays.toString(this.intermediateKeyExpiration) + ", intermediateKeySignature=" + Arrays.toString(this.intermediateKeySignature) + ", salt=" + Arrays.toString(this.salt) + ", payload=" + Arrays.toString(this.payload) + ')';
    }
}
```