GET http://*************:8002/axxxapi737644/users/getuser HTTP/1.1
user-agent: Dart/3.5 (dart:io)
app_version: 7.4.3
accept: application/json
accept-encoding: gzip
authorization: Bearer c93d038a585541d683eeb989cfef91cb
host: *************:8002
content-type: application/json

response:
HTTP/1.1 200 OK
Cache-Control: no-cache
Pragma: no-cache
Content-Type: application/json; charset=utf-8
Expires: -1
Server: Microsoft-IIS/10.0
X-AspNet-Version: 4.0.30319
X-Powered-By: ASP.NET
Date: Thu, 25 Sep 2025 09:04:12 GMT
Content-Length: 1127

```json
{
    "status_code": 200,
    "message": "Request Accepted!",
    "data": "WgWl1lWhYQJEoQrV/bcgGS1od/LuCIWusWGTgbI4bFU42xv8Vchb6S3PJ870SbV2vkgBQkAILbhuYPp1VAgcDbgqWEbQs/K32zJcLdpUEMDWakJhUyekr/v18xo+BTjFLpeBVNu4StiMoKiQBEA09rh0vMW14nfaKTGEcDYWkHSOfN3qVIIOeS4mTZGwc9JJXVcNW0m3OJO6WfX3nk7cw0MTpyYcy7Qv/dX/kJLyFjHa2K84BveO/Y1Mvw+PB4f8yHeVWg7a4MP/13fP2x5CX7RlyF2sHaU3F8Psq/7DQImtwfBRJRYhqCTTPPBBnPITEluKIR6F+/9gXHN0pt3hA6ZWnrTIKzEq5YJ7PZFHquTUuTnHRnyrvxnE5TMQK78dDJgdVgqKcwR+TocezDAWN7ueJkGKyJBHMZWw1fKv1/5X7cUVQ5YYCzYXRyhLa5Rd9nBhVfNRVc8j22dSg+S+C1Kh/mf0y2ut8+ji7e6S4wzaCCNFF7RPSnhxtOkJ5kUwITZAxO/vptrEwraP25blavLvB0O8NPMdhaIcJy/oBw+yHtmjjO+Tge1O3WTXXz12Mh9rTc7zz6J2ySWW026O1QkThjTzVE2NtLwZHPMqnuFD5fRm2y11zTby8DBFpaAwiJrbSxHcuDuQ4Y4phT10/FKtFBEaonZFzD6qYgpVBp8lMPBgJvO9V3p/gAfIJCKvP6PIU1B4/4gqeCZW4GuJATlYeeTht6MdHNw+dZJuZ3jW8moGPmWSZJFxl8p08JmZnBKuWxHrl/6gKvnFVmO+HgHHr4WRbyjjYFULtyuEsARg1l9KYGoDpHBzDf4ZgcGfSNfFByAjw5WvmOrd9X6rW0MaoVvcNDlvCjjHCbd6zyE+kyKFJub0PnFjOjIjV9vqcnQIjpzacRfuHj6OyWSNhBHy9Z30JuWjk0AJ9+WLWYTIffR809KCaDNrfHvaGCH8VINQMocm1AQEm7an4nvc1zWLURRXdil76vXIZESYbcjDPUoCSHdVIG/NQ8qVfKGZKIIchXCHVcYtzyRXrToWWy1tcCFVw9fyI2MSnQUaHQU="
}
```

#####################
# req2:
GET http://*************:8002/axxxapi737644/contacts/getcontact?number=96599436954 HTTP/1.1
user-agent: Dart/3.5 (dart:io)
app_version: 7.4.3
accept: application/json
accept-encoding: gzip
authorization: Bearer c93d038a585541d683eeb989cfef91cb
host: *************:8002
content-type: application/json
x-firebase-appcheck: 

number: '96599436954'

## res:
HTTP/1.1 200 OK
Cache-Control: no-cache
Pragma: no-cache
Content-Type: application/json; charset=utf-8
Expires: -1
Server: Microsoft-IIS/10.0
X-AspNet-Version: 4.0.30319
X-Powered-By: ASP.NET
Date: Thu, 25 Sep 2025 09:01:57 GMT
Content-Length: 94

```json
{
    "status_code": 200,
    "message": "No Data.",
    "data": "jcG51QGfKdPiAesLqTS/fhQQdgUWkOYdfmXdS6fFcZc="
}
```

# Search by name:
GET http://*************:8002/axxxapi737644/contacts/getcontact?name=%D9%87%D8%B4%D8%A7%D9%85+%D9%85%D8%AD%D9%85%D8%AF&country_code=971&offset=0 HTTP/1.1
user-agent: Dart/3.5 (dart:io)
app_version: 7.4.3
accept: application/json
accept-encoding: gzip
authorization: Bearer c93d038a585541d683eeb989cfef91cb
host: *************:8002
content-type: application/json
x-firebase-appcheck: 

name: هشام محمد
country_code: '971'
offset: '0'

# response:
HTTP/1.1 200 OK
Cache-Control: no-cache
Pragma: no-cache
Content-Type: application/json; charset=utf-8
Expires: -1
Server: Microsoft-IIS/10.0
X-AspNet-Version: 4.0.30319
X-Powered-By: ASP.NET
Date: Thu, 25 Sep 2025 09:37:00 GMT
Content-Length: 4319

```json
{
    "status_code": 200,
    "message": "Request Accepted! Data found.",
    "data": "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"
}
```

GET http://*************:8002/axxxapi737644/contacts/getcontact?number=96567757762 HTTP/1.1
user-agent: Dart/3.5 (dart:io)
app_version: 7.4.3
accept: application/json
accept-encoding: gzip
authorization: Bearer c93d038a585541d683eeb989cfef91cb
host: *************:8002
content-type: application/json
x-firebase-appcheck: 
No content
 
 
 
number: '96567757762'


response:
HTTP/1.1 200 OK
Cache-Control: no-cache
Pragma: no-cache
Content-Type: application/json; charset=utf-8
Expires: -1
Server: Microsoft-IIS/10.0
X-AspNet-Version: 4.0.30319
X-Powered-By: ASP.NET
Date: Thu, 25 Sep 2025 09:04:12 GMT
Content-Length: 1651
```json
{
    "status_code": 200,
    "message": "Request Accepted! Data found.",
    "data": "jRSqczJuQMwjlwYJbROy69OVjInL52GGOJPxVWY7mKvvtLk9EWNcfc2sRnTUGQzYcQasufkE4/Gtf2Bw4/YF3z0xK0QY1MzTYlAcAee+rpYx8coZeVpag3Tk5AWcrJ6c4XEaYsToiI0uIcpgfNum50CmhfNuLcXvuadbPNgiEP3yxpDfQJ/HhgmwuXA8bYCPG+FsoISwKMS/4ypELBT8vI+QvIsT0g067o6eM4y8FnHX0agRrZPAptDbmqv2FaPQXWpruX/FjB2D6POZ0mkuOmJMT6/AEAzuDqDkVUVzZMSPz7E2+h2qrzfkb0EQn5ugJ/DwcxrHAODelW0kauk9CsVvri9NRNMW6SAF+96+Al83iJ7Oder+oNIRyXMHFwlrJPN7/y2ShpPlXNRX0P0SYVNo+fDqJPLdVoxCuST67fDk8q6ThNV7e5AT0ghd1IgQk5L9eLpONyUMJfesdMvMSG8S1yGEfPhNhBwYK81qs63+XQ2XhUh7C04519AZCJ1bAvvHq3O4Bg3zRqY827IpBkN2dt17kA9Gzmjw8Z846fOERkOrCpRk617X1j/GrB//gKjcUY3i0M23f5OPXGrEZY4hZngyRviW+q+XCpGkHNzse3eWjnuvXtqVF+oy1r/E7qEte1g8OCv/Z7H13gXtghCrI9hd4ANUtPfU6q4Z1UgNpg+I7Jo9tR/0kgxqK2SSc1NPtfezIqEvyiuM0W46uJXC3RtaMIma3djxZPo3KLXqvRrFS0V5EhGoYICMwL8Ui/Xh5BifdH50SDWD9LLrGV9/ONrlIhLa3B6rYg13z8dPePd26YPHantiosnXb8naD26W+EbJTCOewL+4Yet+C1pIAGf1fm6umCNQ87ZY+mB9mRtSEeeXXw433KyORXZqzJtS9kWGTZLFAwRUdj88xHqccaSEGVcVCH+/gyr1e+aPB8U3zef/AO5gmuwG7Yh82l51t7okHBYP89p6PMkFxN1PQgyijFe20/rJC/TM6qn5c9fbBLMB10z7n+h/UEd/oYlugWczRNy6kCukQuCC7ciFJ/zE3lkliPWjHAjhmJ1xubfO8LCA8H9imOhc+LR8RHlUwjwTg0eGcR5mLvoWhDp4gHWQeVwP8DAF7aRXihg4Z6+M/jOdamCtlORzUXIkcgWE/rAkYqh4c/qiyK6ljNCvqHwRXDcHbK62VcfetvAbmAzXwsziichW6Ta00y3V3NJXyV1rhmEu72u+/849dMUNiZWATauw6QFOSWNDbtev+GzFepvpfLfGETPzNYXemRToqIxbYgVYMSNFYd9yKF2JIiF2BJAsgHjMys52r3YaaW2WPYptXz8vkDt5cPURRIVpD/KRzSPYjDJs+/jBnPuhLa8GsWQFDmQ/AKM8ElpAjYQG5DBmGeEPYLZ7OZjGVxYs/wAxkDsByxxFY+0Hf5/YSwK477yLOvZBt79HeOm4oo59/dusQ0/EmCh8BZ3l4Euu2pMvluBwlJtSgKACR9yomSoJSwRAX+ibOHAfgFwuehd0r+i+B7E0iZtY988vdKjeuZnm6sAv+cBpw3bS8L/c0rvmIEVtQAg2HPtqFrA="
}
```
decrypted:
- Mohamad koet/M
- Ma
- محمد الزلم
- اكس نوكيا

```cmd
curl "http://*************:8002/axxxapi737644/contacts/getcontact?number=96599999999" \
  -H "Authorization: Bearer c93d038a585541d683eeb989cfef91cb" \
  -H "app_version: 7.4.3"
```
# res:
```json
{
    "status_code":200,
    "message":"Request Accepted! Data found.",
    "data":"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"
}
```

GET http://*************:8002/axxxapi737644/users/getuser HTTP/1.1
user-agent: Dart/3.5 (dart:io)
app_version: 7.4.3
accept: application/json
accept-encoding: gzip
authorization: Bearer c93d038a585541d683eeb989cfef91cb
host: *************:8002
content-type: application/json

# Res

HTTP/1.1 200 OK
Cache-Control: no-cache
Pragma: no-cache
Content-Type: application/json; charset=utf-8
Expires: -1
Server: Microsoft-IIS/10.0
X-AspNet-Version: 4.0.30319
X-Powered-By: ASP.NET
Date: Thu, 25 Sep 2025 15:48:24 GMT
Content-Length: 1127

```json
{
    "status_code": 200,
    "message": "Request Accepted!",
    "data": "kFQBPTeR2iLSUyWXwZaXyjG888Nz1+qiRZ5ioZnIX0wlzsPLafJgRdE+5ZDYPBkH4xBx+HekqG4vamVUAX7lD10w7ADXCXBR7tKySPsf8RwB5bMynIAxXS7rYHgutOWP65f7A47JLlXJL0eXYqApP6OrQ0hTtrghJDG32jRmy52s3L8f+rr8bkGoUBZvlx9i4nEPUyT7fpHqtAi6C7+D+es4AmQtvzFtHMXhJ7Rw23LtavJTLnTNTPgew5kl5b0kJojhwZwXfW6z6yVFJbGLurtA0eTvVgrYvvb5QrU5+pxtOZw7VkX0a76I+y79dm50cFODBtiJuOsQUdJZ7ELUmxsi93Dhdh9EtYDAPR9CEjhv94fu3M6VOl+W2LEKTOlEWqFwr9Id8fuqvYsMAj2ZP0U1mh9T40dumiMVS2b4H1YPGKkq6B4JG5ItquLAp+2XDNfZVNLA11KwujrZfB5qXvK8y199p4+OEBhZmjtEj9APdqoNICBgjTb04mDWpjtohU6BSopGPQ2WCu/97G1iK94p9+qCNxozcNGtLGBVQe+s+c5qhXFdRMzgqzBNYfGx+XApUuVgplIg8GraCFA2njuS7uyJdvxGNz7TfCvaCS+k+9174VKlaZW5uLtoDPI6hdZxPjzDSb86zhaUOGQll02htmLZLmRB5zsRC6L8XLhCVW2ZeuoYAkuH9krjG0jRinBLR7AFjA/j3PhbAofluVhPZsGmsBhtDXBk01b679X9oQQA74SP5Hry07fXykS3v+JxjjhN1he8543D76xQWHOf+OM0YTat9Kr1Wxg8PQmTmnj2WgBXwpBAFA90l/zKbL4OQlhFbFuCvE7RVWGIWit5GB5BWKF99Tb9pJABAxByOVeIee9foai68rPQij79q2gcFunEpavAk3jVeceNw52ejq1duNbCSSbm/LKD7jyKwK9Ut1rG5QmSs0GEiMAEn/jNL93KvSQpt2Mbf3GJ3wQkZx/jLthc/anlowtV8o+ScEYbpEGwiQ6IGYdowg1V1ZoXIMh2vAIbjColA5Y4WammrrTIgjcgwVq7mWGLGl0="
}
```
علي أسماعيل ياغي
96599434954